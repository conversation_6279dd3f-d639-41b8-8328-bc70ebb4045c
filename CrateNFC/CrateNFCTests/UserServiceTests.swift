import Foundation
import SwiftData
import XCTest

@testable import CrateServices

final class UserServiceTests: XCTestCase {
  // MARK: - Test Properties

  private var mockHTTPClient: MockHTTPClient!
  private var mockUserState: MockUserState!
  private var userService: UserService!

  override func setUp() {
    super.setUp()
    mockHTTPClient = MockHTTPClient()
    mockUserState = MockUserState()
    userService = UserService(client: mockHTTPClient, userState: mockUserState)
  }

  override func tearDown() {
    mockHTTPClient = nil
    mockUserState = nil
    userService = nil
    super.tearDown()
  }

  class MockUserState: UserState {
    var mockIsSignedIn = false
    var mockToken: String?
    var logoutCalled = false
    var loginUser: User?
    var loginToken: String?

    override var isSignedIn: Bool {
      return mockIsSignedIn
    }

    override var token: String? {
      get { return mockToken }
      set { mockToken = newValue }
    }

    override var currentUser: User? {
      get { return super.currentUser }
      set { super.currentUser = newValue }
    }

    override func login(user: User, token: String) {
      loginUser = user
      loginToken = token
      mockIsSignedIn = true
      mockToken = token
      currentUser = user
    }

    override func logout() {
      logoutCalled = true
      mockIsSignedIn = false
      mockToken = nil
      currentUser = nil
    }
  }

  // MARK: - Test Setup

  var mockClient: MockHTTPClient!
  var mockUserState: MockUserState!
  var userService: UserService!

  // MARK: - Test Cases

  func testLoginSuccess() async {
    // Note: This test is challenging because UserService uses MSAL authentication
    // which involves delegate methods and continuations.
    // For a proper test, you would need to mock the MSAL components or
    // create a testable version of UserService that doesn't use MSAL.

    // For now, we'll skip this test or create a simplified version
    // that tests the core logic without MSAL
  }

  func testLoginFailure() async {
    // Similar to testLoginSuccess, this would require mocking MSAL
    // Skip for now or implement with MSAL mocking
  }

  func testLogout() {
    // When
    userService.logout()

    // Then
    XCTAssertTrue(mockUserState.logoutCalled)
    XCTAssertTrue(mockHTTPClient.bearerTokenRemoved)
  }

}
