import Foundation
import SwiftData

// MARK: - Collection Service Protocol

public protocol CollectionServiceProtocol {
  // Remote collections endpoint
  func getCollections(start: Int, size: Int) async throws -> [CollectionDTO]
  func createEmptyCollection(name: String, thumbnail: String?) async throws -> ServerCollectionDTO
  func deleteCollection(serverId: Int) async throws -> Bool
  func addTrackToCollection(collectionServerId: Int, trackServerId: Int) async throws -> Bool
  func removeTrackFromCollection(collectionServerId: Int, trackServerId: Int) async throws -> Bool

  // Local collections management
  func saveCollections(_ collections: [Collection], context: ModelContext) throws
  func deleteAllCollections(context: ModelContext) throws
  func getAllCollections(context: ModelContext) throws -> [Collection]
  func getCollectionByServerId(_ serverId: Int, context: ModelContext) throws -> Collection?
}

// MARK: - Server-side DTOs

public struct ServerTrackDTO: Codable {
  public let id: Int
  public let trackTitle: String
  public let artistName: String
  public let url: String
  public let mediaUrl: String?
  public let domainUrl: String?
  public let duration: Int?
  public let isrc: String?
  public let created: String
  public let updated: String

  public func toTrackModel() -> Track {
    let isoFormatter = ISO8601DateFormatter()
    print("CREATING TRACK MODEL \(id) \(trackTitle)")
    return Track(
      serverId: id,  // Store the server ID
      name: trackTitle,
      artist: artistName,
      imgUrl: mediaUrl,
      url: url,
      domain: domainUrl,
      updatedAt: isoFormatter.date(from: updated) ?? Date.distantPast,
      createdAt: isoFormatter.date(from: created) ?? Date.distantPast
    )
  }
}

public struct ServerCollectionDTO: Codable {
  public let id: Int
  public let name: String
  public let thumbnail: String
  public let tracks: [ServerTrackDTO]
  public let created: String
  public let updated: String

  public init(
    id: Int,
    name: String,
    thumbnail: String,
    tracks: [ServerTrackDTO],
    created: String,
    updated: String
  ) {
    self.id = id
    self.name = name
    self.thumbnail = thumbnail
    self.tracks = tracks
    self.created = created
    self.updated = updated
  }

  public func toCollectionModel() -> Collection {
    let isoFormatter = ISO8601DateFormatter()
    return Collection(
      serverId: id,
      name: name,
      tracks: [],
      thumbnail: thumbnail,
      createdAt: isoFormatter.date(from: created) ?? Date.distantPast,
      updatedAt: isoFormatter.date(from: updated) ?? Date.distantPast
    )
  }
}

private struct CreateCollectionRequestDTO: Codable {
  let name: String
  let thumbnail: String
  // Empty collection - no tracks included
}

private struct EmptyParameters: Encodable {}

// MARK: - Collection Service Implementation

public struct CollectionService: CollectionServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState

  public init(client: HTTPClientProtocol = HTTPClient(), userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState
  }

  // MARK: - GET Collections Endpoint

  public func getCollections(start: Int = 0, size: Int = 20) async throws -> [CollectionDTO] {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    struct CollectionParams: Codable {
      let start: Int
      let size: Int
    }

    let params = CollectionParams(start: start, size: size)

    do {
      let serverCollections: [ServerCollectionDTO] = try await client.get(
        path: "/api/v1/collection",
        parameters: params
      )

      let isoFormatter = ISO8601DateFormatter()
      return serverCollections.map { collection in
        CollectionDTO(
          serverId: collection.id,
          name: collection.name,
          thumbnail: collection.thumbnail,
          tracks: collection.tracks.map { track in
            TrackDTO(
              serverId: track.id,
              name: track.trackTitle,
              artist: track.artistName,
              imgUrl: track.mediaUrl,
              url: track.url,
              domain: track.domainUrl,
              recentCollections: nil,
              collections: nil,
              updatedAt: isoFormatter.date(from: track.updated) ?? Date.distantPast,
              createdAt: isoFormatter.date(from: track.created) ?? Date.distantPast
            )
          },
          createdAt: isoFormatter.date(from: collection.created) ?? Date.distantPast,
          updatedAt: isoFormatter.date(from: collection.updated) ?? Date.distantPast
        )
      }
    } catch {
      print("🔴 Get collections error: \(error.localizedDescription)")
      throw CollectionServiceError.getCollectionsFailed
    }
  }

  // MARK: - POST Collection Endpoint

  public func createEmptyCollection(name: String, thumbnail: String? = nil) async throws
    -> ServerCollectionDTO
  {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    let requestDTO = CreateCollectionRequestDTO(
      name: name,
      thumbnail: thumbnail ?? ""
    )

    do {
      print("📤 Sending empty collection creation request with name: \(name)")

      let serverCollection: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection",
        body: requestDTO
      )

      return serverCollection
    } catch {
      print("🔴 Collection creation error: \(error.localizedDescription)")
      throw CollectionServiceError.createCollectionFailed
    }
  }

  // MARK: - Add Track to Collection Endpoint

  public func addTrackToCollection(collectionServerId: Int, trackServerId: Int) async throws -> Bool
  {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      let _: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection/\(collectionServerId)/add",
        body: [trackServerId]
      )
      return true
    } catch {
      print("🔴 Adding track to collection error: \(error.localizedDescription)")
      throw CollectionServiceError.addTrackToCollectionFailed
    }
  }

  // MARK: - Remove Track from Collection Endpoint

  public func removeTrackFromCollection(collectionServerId: Int, trackServerId: Int) async throws
    -> Bool
  {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      let _: ServerCollectionDTO = try await client.post(
        path: "/api/v1/collection/\(collectionServerId)/remove",
        body: [trackServerId]
      )
      return true
    } catch {
      print("🔴 Removing track from collection error: \(error.localizedDescription)")
      throw CollectionServiceError.removeTrackFromCollectionFailed
    }
  }

  // MARK: - DELETE Collection Endpoint

  public func deleteCollection(serverId: Int) async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw CollectionServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/collection/\(serverId)", body: EmptyParameters())
      return true
    } catch {
      print("🔴 Collection deletion error: \(error.localizedDescription)")
      throw CollectionServiceError.deleteCollectionFailed
    }
  }

  // MARK: - Collection persistence methods

  public func saveCollections(_ collections: [Collection], context: ModelContext) throws {
    print("SAVING COLLECTIONS")
    collections.forEach { context.insert($0) }
    try context.save()
  }

  public func deleteAllCollections(context: ModelContext) throws {
    // Fetch all Collections
    let collections = try context.fetch(FetchDescriptor<Collection>())
    print("deleteAllCollections xxx")

    for collection in collections {
      // Detach all MTM relationships first
      collection.tracks = []
      context.delete(collection)
    }
    try context.save()
  }

  public func getAllCollections(context: ModelContext) throws -> [Collection] {
    let descriptor = FetchDescriptor<Collection>()
    return try context.fetch(descriptor)
  }

  public func getCollectionByServerId(_ serverId: Int, context: ModelContext) throws -> Collection?
  {
    let predicate = #Predicate<Collection> { collection in
      collection.serverId == serverId
    }

    let descriptor = FetchDescriptor<Collection>(predicate: predicate)
    let collections = try context.fetch(descriptor)
    return collections.first
  }
}

// MARK: - Collection Service Errors

public enum CollectionServiceError: Error {
  case notAuthenticated
  case getCollectionsFailed
  case createCollectionFailed
  case deleteCollectionFailed
  case addTrackToCollectionFailed
  case removeTrackFromCollectionFailed
}
