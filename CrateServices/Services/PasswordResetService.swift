import Foundation
import MSAL
import SwiftData

public protocol PasswordResetServiceProtocol {
  func startPasswordReset(email: String) async throws -> PasswordResetResult
  func submitVerificationCode(_ code: String) async throws -> PasswordResetResult
  func submitNewPassword(_ password: String) async throws -> PasswordResetResult
  func resendVerificationCode() async throws
}

public enum PasswordResetResult {
  case verificationRequired(sentTo: String)
  case newPasswordRequired
  case completed
}

public enum PasswordResetServiceError: Error {
  case userNotFound
  case invalidVerificationCode
  case invalidPassword
  case resetFailed
  case noActiveReset
  case browserRequired
}

public class PasswordResetService: PasswordResetServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState
  private let authPublisher = AuthPublisher.shared
  private var nativeAuth: MSALNativeAuthPublicClientApplication!
  private let userService: UserServiceProtocol

  // Reset state management
  private var resetContinuation: CheckedContinuation<PasswordResetResult, Error>?
  private var verificationContinuation: CheckedContinuation<PasswordResetResult, Error>?
  private var passwordContinuation: CheckedContinuation<PasswordResetResult, Error>?
  private var resetCodeRequiredState: ResetPasswordCodeRequiredState?
  private var newPasswordState: ResetPasswordRequiredState?

  public init(
    client: HTTPClientProtocol = HTTPClient(), userState: UserState = UserState.shared,
    userService: UserServiceProtocol
  ) {
    self.client = client
    self.userState = userState
    self.userService = userService

    // Access the MSAL instance from UserService
    if let userServiceImpl = userService as? UserService,
      let sharedMSAL = userServiceImpl.getMSALInstance()
    {
      nativeAuth = sharedMSAL
      print("Initialized PasswordResetService using shared MSAL instance")
    } else {
      // Fallback: create our own instance
      do {
        nativeAuth = try MSALNativeAuthPublicClientApplication(
          clientId: Configuration.clientId,
          tenantSubdomain: Configuration.tenantSubdomain,
          challengeTypes: [.OOB, .password]
        )
        print("Initialized MSALNativeAuthPublicClientApplication for Password Reset (fallback)")
      } catch {
        print("Unable to initialize MSAL for Password Reset: \(error)")
      }
    }
  }

  public func startPasswordReset(email: String) async throws -> PasswordResetResult {
    return try await withCheckedThrowingContinuation { continuation in
      self.resetContinuation = continuation

      let parameters = MSALNativeAuthResetPasswordParameters(username: email)
      nativeAuth.resetPassword(parameters: parameters, delegate: self)
    }
  }

  public func submitVerificationCode(_ code: String) async throws -> PasswordResetResult {
    guard let resetState = resetCodeRequiredState else {
      throw PasswordResetServiceError.noActiveReset
    }

    return try await withCheckedThrowingContinuation { continuation in
      self.verificationContinuation = continuation
      resetState.submitCode(code: code, delegate: self)
    }
  }

  public func submitNewPassword(_ password: String) async throws -> PasswordResetResult {
    guard let passwordState = newPasswordState else {
      throw PasswordResetServiceError.noActiveReset
    }

    return try await withCheckedThrowingContinuation { continuation in
      self.passwordContinuation = continuation
      passwordState.submitPassword(password: password, delegate: self)
    }
  }

  public func resendVerificationCode() async throws {
    guard let resetState = resetCodeRequiredState else {
      throw PasswordResetServiceError.noActiveReset
    }

    resetState.resendCode(delegate: self)
  }

  private func showResultText(_ text: String) {
    print(text)
  }
}

// MARK: - ResetPasswordStartDelegate

extension PasswordResetService: ResetPasswordStartDelegate {
  public func onResetPasswordStartError(error: MSAL.ResetPasswordStartError) {
    showResultText("ResetPasswordStartDelegate: onResetPasswordStartError: \(error)")

    if let continuation = resetContinuation {
      if error.isUserNotFound {
        showResultText("User not found")
        continuation.resume(throwing: PasswordResetServiceError.userNotFound)
      } else {
        showResultText(
          "Error starting password reset: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: PasswordResetServiceError.resetFailed)
      }
      resetContinuation = nil
    }
  }

  public func onResetPasswordCodeRequired(
    newState: MSAL.ResetPasswordCodeRequiredState,
    sentTo: String,
    channelTargetType: MSAL.MSALNativeAuthChannelType,
    codeLength: Int
  ) {
    showResultText("ResetPasswordStartDelegate: onResetPasswordCodeRequired")

    // Store the state for verification
    resetCodeRequiredState = newState

    // Resume with verification required result
    if let continuation = resetContinuation {
      continuation.resume(returning: .verificationRequired(sentTo: sentTo))
      resetContinuation = nil
    }
  }
}

// MARK: - ResetPasswordVerifyCodeDelegate

extension PasswordResetService: ResetPasswordVerifyCodeDelegate {
  public func onResetPasswordVerifyCodeError(
    error: MSAL.VerifyCodeError, newState: MSAL.ResetPasswordCodeRequiredState?
  ) {
    showResultText("ResetPasswordVerifyCodeDelegate: onResetPasswordVerifyCodeError: \(error)")

    if let continuation = verificationContinuation {
      if error.isInvalidCode {
        showResultText("Invalid verification code")
        continuation.resume(throwing: PasswordResetServiceError.invalidVerificationCode)
      } else if error.isBrowserRequired {
        showResultText("Browser required")
        continuation.resume(throwing: PasswordResetServiceError.browserRequired)
      } else {
        showResultText("Error verifying code: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: PasswordResetServiceError.resetFailed)
      }
      verificationContinuation = nil
    }

    // Update state if provided
    if let newState = newState {
      resetCodeRequiredState = newState
    }
  }

  public func onPasswordRequired(newState: MSAL.ResetPasswordRequiredState) {
    showResultText("Password required for reset")

    // Store the state for password submission
    newPasswordState = newState

    // Resume with new password required result
    if let continuation = verificationContinuation {
      continuation.resume(returning: .newPasswordRequired)
      verificationContinuation = nil
    }
  }
}

// MARK: - ResetPasswordResendCodeDelegate

extension PasswordResetService: ResetPasswordResendCodeDelegate {
  public func onResetPasswordResendCodeError(
    error: MSAL.ResendCodeError, newState: MSAL.ResetPasswordCodeRequiredState?
  ) {
    showResultText("ResetPasswordResendCodeDelegate: onResetPasswordResendCodeError: \(error)")
    // For resend errors, we don't need to resume any continuation
    // The UI can handle this as needed
  }

  public func onResetPasswordResendCodeRequired(
    newState: MSAL.ResetPasswordCodeRequiredState,
    sentTo: String,
    channelTargetType: MSAL.MSALNativeAuthChannelType,
    codeLength: Int
  ) {
    showResultText("Reset code resent successfully")
    resetCodeRequiredState = newState
  }
}

// MARK: - ResetPasswordRequiredDelegate

extension PasswordResetService: ResetPasswordRequiredDelegate {
  public func onResetPasswordRequiredError(
    error: MSAL.PasswordRequiredError, newState: MSAL.ResetPasswordRequiredState?
  ) {
    showResultText("ResetPasswordRequiredDelegate: onResetPasswordRequiredError: \(error)")

    if let continuation = passwordContinuation {
      if error.isInvalidPassword {
        showResultText("Invalid password")
        continuation.resume(throwing: PasswordResetServiceError.invalidPassword)
      } else {
        showResultText(
          "Error setting password: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: PasswordResetServiceError.resetFailed)
      }
      passwordContinuation = nil
    }

    // Update state if provided
    if let newState = newState {
      newPasswordState = newState
    }
  }

  public func onResetPasswordCompleted(newState: MSAL.SignInAfterResetPasswordState) {
    showResultText("Password reset completed successfully")

    if let continuation = passwordContinuation {
      continuation.resume(returning: .completed)
      passwordContinuation = nil
    }

    // Clear reset states
    resetCodeRequiredState = nil
    newPasswordState = nil

    // Attempt to sign in with the new password
    let parameters = MSALNativeAuthSignInAfterResetPasswordParameters()
    newState.signIn(parameters: parameters, delegate: self)
  }
}

// MARK: - SignInAfterResetPasswordDelegate

extension PasswordResetService: SignInAfterResetPasswordDelegate {
  public func onSignInAfterResetPasswordError(error: MSAL.SignInAfterResetPasswordError) {
    showResultText("SignInAfterResetPasswordDelegate: onSignInAfterResetPasswordError: \(error)")
    // We don't need to handle this error as the password reset was successful
    // The user can sign in manually if needed
  }
}
